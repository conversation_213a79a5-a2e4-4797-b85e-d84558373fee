import json
import re
from uuid import uuid4
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, asdict

@dataclass
class MasterPractice:
    practice_uuid: str
    golden_name: str
    golden_address: str
    golden_city: str
    golden_state: str
    golden_zip: str
    golden_phone: str
    created_at: str

class DataNormalizer:
    @staticmethod
    def normalize_phone(phone: str) -> str:
        if not phone:
            return ""
        return re.sub(r'[^\d]', '', phone)

    @staticmethod
    def normalize_address(address: str) -> str:
        if not address:
            return ""
        return re.sub(r'[^\w\s]', '', address.upper()).strip()

    @staticmethod
    def normalize_zip(zip_code: str) -> str:
        if not zip_code:
            return ""
        return zip_code[:5]

    @staticmethod
    def normalize_name(name: str) -> str:
        if not name:
            return ""
        return re.sub(r'[^\w\s]', '', name.upper()).strip()

class MatchingKeyGenerator:
    def __init__(self, normalizer: DataNormalizer):
        self.normalizer = normalizer

    def generate_keys(self, name: str, address: str, city: str, state: str, zip_code: str, phone: str) -> List[str]:
        norm_name = self.normalizer.normalize_name(name)
        norm_address = self.normalizer.normalize_address(address)
        norm_city = self.normalizer.normalize_name(city)
        norm_state = state.upper() if state else ""
        norm_zip = self.normalizer.normalize_zip(zip_code)
        norm_phone = self.normalizer.normalize_phone(phone)

        keys = []
        
        if norm_phone and norm_zip:
            keys.append(f"phone_zip:{norm_phone}:{norm_zip}")
        
        if norm_name and norm_zip:
            keys.append(f"name_zip:{norm_name}:{norm_zip}")
        
        if norm_address and norm_city and norm_state:
            keys.append(f"address_city_state:{norm_address}:{norm_city}:{norm_state}")
        
        if norm_phone and norm_city and norm_state:
            keys.append(f"phone_city_state:{norm_phone}:{norm_city}:{norm_state}")

        return keys

class NPIDataExtractor:
    @staticmethod
    def extract_practice_data(npi_record: Dict) -> Tuple[str, str, str, str, str, str]:
        if 'org_name' in npi_record:
            name = npi_record.get('org_name', '')
            if npi_record.get('other_names'):
                name = npi_record['other_names'][0].get('name', name)
        else:
            first_name = npi_record.get('first_name', '')
            last_name = npi_record.get('last_name', '')
            name = f"{first_name} {last_name}".strip()

        address = npi_record.get('address', '')
        city = npi_record.get('city', '')
        state = npi_record.get('state', '')
        zip_code = npi_record.get('zip_code', '')
        phone = npi_record.get('phone', '')

        return name, address, city, state, zip_code, phone

class BBBDataExtractor:
    @staticmethod
    def extract_practice_data(bbb_record: Dict) -> Tuple[str, str, str, str, str, str]:
        name = bbb_record.get('name', '')
        address = bbb_record.get('street', '')
        city = bbb_record.get('city', '')
        state = bbb_record.get('state', '') or bbb_record.get('province', '')
        zip_code = bbb_record.get('zip', '') or bbb_record.get('postal_code', '')
        phone = bbb_record.get('phone', '')

        return name, address, city, state, zip_code, phone

class PracticeLinker:
    def __init__(self):
        self.normalizer = DataNormalizer()
        self.key_generator = MatchingKeyGenerator(self.normalizer)
        self.npi_extractor = NPIDataExtractor()
        self.bbb_extractor = BBBDataExtractor()
        self.master_practices = []
        self.practice_index = {}

    def load_master_practices(self, file_path: str) -> None:
        try:
            with open(file_path, 'r') as f:
                data = json.load(f)
                self.master_practices = [MasterPractice(**practice) for practice in data.get('practices', [])]
                self._rebuild_index()
        except FileNotFoundError:
            self.master_practices = []
            self.practice_index = {}

    def save_master_practices(self, file_path: str) -> None:
        data = {
            'practices': [asdict(practice) for practice in self.master_practices]
        }
        with open(file_path, 'w') as f:
            json.dump(data, f, indent=2)

    def _rebuild_index(self) -> None:
        self.practice_index = {}
        for practice in self.master_practices:
            keys = self.key_generator.generate_keys(
                practice.golden_name,
                practice.golden_address,
                practice.golden_city,
                practice.golden_state,
                practice.golden_zip,
                practice.golden_phone
            )
            for key in keys:
                self.practice_index[key] = practice.practice_uuid

    def _find_matching_practice(self, keys: List[str]) -> Optional[str]:
        for key in keys:
            if key in self.practice_index:
                return self.practice_index[key]
        return None

    def _create_master_practice(self, name: str, address: str, city: str, state: str, zip_code: str, phone: str) -> str:
        practice_uuid = str(uuid4())
        master_practice = MasterPractice(
            practice_uuid=practice_uuid,
            golden_name=name if name else "",
            golden_address=address.upper() if address else "",
            golden_city=city.upper() if city else "",
            golden_state=state.upper() if state else "",
            golden_zip=self.normalizer.normalize_zip(zip_code),
            golden_phone=self.normalizer.normalize_phone(phone),
            created_at=datetime.now().isoformat()
        )
        
        self.master_practices.append(master_practice)
        
        keys = self.key_generator.generate_keys(name, address, city, state, zip_code, phone)
        for key in keys:
            self.practice_index[key] = practice_uuid
        
        return practice_uuid

    def _update_data_with_uuid(self, data: List[Dict], uuid_key: str) -> None:
        for record in data:
            record[uuid_key] = record.get(uuid_key, None)

    def link_bbb_data(self, bbb_data: List[Dict]) -> None:
        for record in bbb_data:
            name, address, city, state, zip_code, phone = self.bbb_extractor.extract_practice_data(record)
            keys = self.key_generator.generate_keys(name, address, city, state, zip_code, phone)
            
            existing_uuid = self._find_matching_practice(keys)
            if existing_uuid:
                record['practice_uuid'] = existing_uuid
            else:
                new_uuid = self._create_master_practice(name, address, city, state, zip_code, phone)
                record['practice_uuid'] = new_uuid

    def link_npi_data(self, npi_data: List[Dict]) -> None:
        for record in npi_data:
            name, address, city, state, zip_code, phone = self.npi_extractor.extract_practice_data(record)
            keys = self.key_generator.generate_keys(name, address, city, state, zip_code, phone)
            
            existing_uuid = self._find_matching_practice(keys)
            if existing_uuid:
                record['practice_uuid'] = existing_uuid
            else:
                new_uuid = self._create_master_practice(name, address, city, state, zip_code, phone)
                record['practice_uuid'] = new_uuid

    def process_all_data(self, bbb_file: str, npi_file: str, master_file: str) -> None:
        self.load_master_practices(master_file)
        
        with open(bbb_file, 'r') as f:
            bbb_data = json.load(f)
        
        with open(npi_file, 'r') as f:
            npi_data = json.load(f)
        
        self.link_bbb_data(bbb_data)
        self.link_npi_data(npi_data)
        
        self.save_master_practices(master_file)
        
        with open(bbb_file, 'w') as f:
            json.dump(bbb_data, f, indent=2)
        
        with open(npi_file, 'w') as f:
            json.dump(npi_data, f, indent=2)

if __name__ == "__main__":
    linker = PracticeLinker()
    linker.process_all_data(r'dataToMap\dentalpractices.json', r'dataToMap\NPI_data.json', 'master_practices.json')