# Leads Generator

Codebase to generate and manage dental leads.

## Components

### NPI Registry Client
- Fetches dentist data from the NPI Registry API
- Supports pagination and filtering by location

### BBB Leads Extractor
- Extracts business information from Better Business Bureau profiles
- Multiple versions with performance optimizations
- Supports both single file and batch processing
- Parallel processing capabilities for large datasets

## Getting Started

1. For NPI data: See `NPIs/sample_usage.py` for example usage
2. For BBB data: Use the extractor scripts in the `bbb-leads-extractor` directory

## Data Sources
- NPI Registry API (healthcare provider information)
- Better Business Bureau business profiles

## Naming Conventions
- Lower Case/Pascal Case for folder names. numberofdonuts/NumberOfDonuts
- Camel Case for file names. numberOfDonuts
- Snake Case for variable names. number_of_donuts
- Snake Case for function names. number_of_donuts()
