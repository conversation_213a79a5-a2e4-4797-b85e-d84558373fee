from npiregistry_client import fetch_paginated
import json

count = 0
results = []
for dentist in fetch_paginated(
    taxonomy_description="Dentist", city="New York", state="NY", batch_size=200, max_records=1200,
):
    results.append(dentist)
    count += 1

print(f"Retrieved {count} dentist records in NY.")

with open("output/dentists_ny.json", "w") as f:
    json.dump(results, f, indent=2)
    
print(f"Results saved to dentists_ny.json")

    
