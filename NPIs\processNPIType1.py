import csv
import json

def process_npi_data(npi_main_filepath, pl_filepath, ep_filepath, output_json_filepath, dental_taxonomy_codes):
    """
    Processes NPI data files to extract and filter information for Type 1 (Individual) dentists.
    Combines data from main NPI file, practice locations, and endpoints.
    """

    practice_locations = {}
    endpoints = {}
    extracted_dentists = []

    # Columns to extract for Type 1 NPI (Individual Dentists)
    # These match the non-nested JSON structure described earlier
    target_columns = [
        "NPI",
        "Provider Last Name (Legal Name)",
        "Provider First Name",
        "Provider Credential Text",
        "Provider First Line Business Practice Location Address",
        "Provider Business Practice Location Address City Name",
        "Provider Business Practice Location Address State Name",
        "Provider Business Practice Location Address Postal Code",
        "Provider Business Practice Location Address Telephone Number",
        "Provider Enumeration Date",
        "Last Update Date",
        "Provider Sex Code"
    ]

    # Load Practice Locations Reference File
    try:
        with open(pl_filepath, 'r', encoding='utf-8') as f:
            reader = csv.reader(f)
            header = [col.strip() for col in next(reader)]
            # Map column names to their indices for efficient lookup
            pl_indices = {col: header.index(col) for col in [
                "NPI",
                "Provider Secondary Practice Location Address- Address Line 1",
                "Provider Secondary Practice Location Address-  Address Line 2",
                "Provider Secondary Practice Location Address - City Name",
                "Provider Secondary Practice Location Address - State Name",
                "Provider Secondary Practice Location Address - Postal Code",
                "Provider Secondary Practice Location Address - Country Code (If outside U.S.)",
                "Provider Secondary Practice Location Address - Telephone Number",
                "Provider Secondary Practice Location Address - Telephone Extension"
            ]}
            for row in reader:
                npi = row[pl_indices["NPI"]]
                location_data = {
                    "address_line_1": row[pl_indices["Provider Secondary Practice Location Address- Address Line 1"]],
                    "address_line_2": row[pl_indices["Provider Secondary Practice Location Address-  Address Line 2"]],
                    "city": row[pl_indices["Provider Secondary Practice Location Address - City Name"]],
                    "state": row[pl_indices["Provider Secondary Practice Location Address - State Name"]],
                    "postal_code": row[pl_indices["Provider Secondary Practice Location Address - Postal Code"]],
                    "country_code": row[pl_indices["Provider Secondary Practice Location Address - Country Code (If outside U.S.)"]],
                    "telephone": row[pl_indices["Provider Secondary Practice Location Address - Telephone Number"]],
                    "telephone_ext": row[pl_indices["Provider Secondary Practice Location Address - Telephone Extension"]]
                }
                practice_locations.setdefault(npi, []).append(location_data)
    except FileNotFoundError:
        print(f"Warning: Practice Location file not found at {pl_filepath}. Continuing without secondary locations.")
    except Exception as e:
        print(f"Error loading Practice Location file: {e}. Skipping secondary locations.")

    # Load Endpoints Reference File
    try:
        with open(ep_filepath, 'r', encoding='utf-8') as f:
            reader = csv.reader(f)
            header = [col.strip() for col in next(reader)]
            ep_indices = {col: header.index(col) for col in [
                "NPI",
                "Endpoint Type",
                "Endpoint",
                "Use Code",
                "Content Type",
                "Affiliation Legal Business Name" # Useful to see what organization this endpoint is associated with, even for Type 1
            ]}
            for row in reader:
                npi = row[ep_indices["NPI"]]
                endpoint_data = {
                    "type": row[ep_indices["Endpoint Type"]],
                    "endpoint": row[ep_indices["Endpoint"]],
                    "use_code": row[ep_indices["Use Code"]],
                    "content_type": row[ep_indices["Content Type"]],
                    "affiliation_lbn": row[ep_indices["Affiliation Legal Business Name"]]
                }
                endpoints.setdefault(npi, []).append(endpoint_data)
    except FileNotFoundError:
        print(f"Warning: Endpoint file not found at {ep_filepath}. Continuing without endpoint data.")
    except Exception as e:
        print(f"Error loading Endpoint file: {e}. Skipping endpoint data.")

    # Process Main NPI Data File
    try:
        with open(npi_main_filepath, 'r', encoding='utf-8') as f:
            reader = csv.reader(f)
            main_header = [col.strip() for col in next(reader)]
            main_indices = {col: main_header.index(col) for col in target_columns + [
                "Entity Type Code"
            ]}
            # Add taxonomy columns to indices, handling up to 15
            for i in range(1, 16):
                col_name = f"Healthcare Provider Taxonomy Code_{i}"
                if col_name in main_header:
                    main_indices[col_name] = main_header.index(col_name)
                
                col_name_lic = f"Provider License Number_{i}"
                if col_name_lic in main_header:
                    main_indices[col_name_lic] = main_header.index(col_name_lic)

                col_name_lic_state = f"Provider License Number State Code {i}"
                if col_name_lic_state in main_header:
                    main_indices[col_name_lic_state] = main_header.index(col_name_lic_state)
                    
                col_name_primary_switch = f"Healthcare Provider Primary Taxonomy Switch_{i}"
                if col_name_primary_switch in main_header:
                    main_indices[col_name_primary_switch] = main_header.index(col_name_primary_switch)

            # Process each row
            for row in reader:
                if row[main_indices["Entity Type Code"]] == '1': # Check for Type 1 NPI
                    is_dentist = False
                    found_taxonomies = []
                    found_licenses = []
                    for i in range(1, 16):
                        tax_col = f"Healthcare Provider Taxonomy Code_{i}"
                        lic_col = f"Provider License Number_{i}"
                        lic_state_col = f"Provider License Number State Code {i}"
                        primary_switch_col = f"Healthcare Provider Primary Taxonomy Switch_{i}"

                        if tax_col in main_indices and len(row) > main_indices[tax_col]:
                            taxonomy_code = row[main_indices[tax_col]].strip()
                            if taxonomy_code in dental_taxonomy_codes:
                                is_dentist = True
                                taxonomy_info = {
                                    "code": taxonomy_code
                                }
                                if lic_col in main_indices and len(row) > main_indices[lic_col]:
                                    taxonomy_info["license_number"] = row[main_indices[lic_col]]
                                if lic_state_col in main_indices and len(row) > main_indices[lic_state_col]:
                                    taxonomy_info["license_state"] = row[main_indices[lic_state_col]]
                                if primary_switch_col in main_indices and len(row) > main_indices[primary_switch_col]:
                                    taxonomy_info["is_primary"] = row[main_indices[primary_switch_col]]
                                found_taxonomies.append(taxonomy_info)

                    if is_dentist:
                        npi_record = {col: row[main_indices[col]] for col in target_columns}
                        npi = npi_record["NPI"]
                        
                        npi_record["dental_taxonomies"] = found_taxonomies
                        
                        if npi in practice_locations:
                            npi_record["secondary_practice_locations"] = practice_locations[npi]
                        else:
                            npi_record["secondary_practice_locations"] = []

                        if npi in endpoints:
                            npi_record["endpoints"] = endpoints[npi]
                        else:
                            npi_record["endpoints"] = []

                        extracted_dentists.append(npi_record)

    except FileNotFoundError:
        print(f"Error: Main NPI data file not found at {npi_main_filepath}. Script aborted.")
        return
    except Exception as e:
        print(f"Error processing main NPI data file: {e}. Script aborted.")
        return

    # Save results to JSON file
    try:
        with open(output_json_filepath, 'w', encoding='utf-8') as outfile:
            json.dump(extracted_dentists, outfile, indent=4)
        print(f"Successfully processed Type 1 NPI data for dentists. Results saved to {output_json_filepath}")
    except Exception as e:
        print(f"Error saving results to JSON file: {e}")

# --- Example Usage ---
if __name__ == "__main__":
    # Define file paths (replace with your actual file paths)
    NPI_MAIN_FILE = "NPI-weekly-data/main.csv" # Example name, adjust to your monthly file
    PL_FILE = "NPI-weekly-data/pracLoc.csv" # Example name
    EP_FILE = "NPI-weekly-data/endpoint.csv" # Example name
    OUTPUT_JSON_FILE_TYPE1 = "dentists_type1_npi_data.json"

    # Dental Taxonomy Codes provided by the user
    DENTAL_TAXONOMY_CODES = {
        "125K00000X", "126800000X", "124Q00000X", "126900000X", "125J00000X",
        "122300000X", "1223D0004X", "1223D0001X", "1223E0200X", "1223G0001X",
        "1223P0106X", "1223X0008X", "1223S0112X", "125Q00000X", "1223X2210X",
        "1223X0400X", "1223P0221X", "1223P0300X", "1223P0700X", "122400000X"
    }

    # Run the processing for Type 1 NPIs
    process_npi_data(NPI_MAIN_FILE, PL_FILE, EP_FILE, OUTPUT_JSON_FILE_TYPE1, DENTAL_TAXONOMY_CODES)
