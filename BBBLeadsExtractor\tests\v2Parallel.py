import re
import json
import uuid
from datetime import datetime
from typing import Dict, Any, Optional
from bs4 import BeautifulSoup
import time
from pathlib import Path
from concurrent.futures import ProcessPoolExecutor, as_completed

class BBBProfileExtractor:
    def __init__(self):
        self.skip_domains = [
            'facebook', 'twitter', 'linkedin', 'instagram', 'youtube', 'bbb.org',
            'bbbprograms.org', 'give.org', 'google.com', 'maps.google',
            'googletagmanager', 'mouseflow.com', 'cloudflareinsights', 'e2ma.net',
            'mailchimp.com', 'constantcontact.com', 'signup.', 'newsletter.',
            'livechatinc.com', 'subscribe.'
        ]

    def extract_profile(self, html_content: str) -> Dict[str, Any]:
        if not html_content or not html_content.strip():
            raise ValueError("HTML content cannot be empty")

        soup = BeautifulSoup(html_content, 'html.parser')

        data = {
            "id": str(uuid.uuid4()),
            "dso_id": None,
            "name": None,
            "street": None,
            "city": None,
            "state": None,
            "zip": None,
            "zipcode_extended": None,
            "phone": None,
            "email": None,
            "website": None,
            "date_opened": None,
            "status": "Active",
            "bbb_rating": None,
            "local_bbb": None,
            "bbb_file_opened": None,
            "business_started": None,
            "business_started_locally": None,
            "business_incorporated": None,
            "type_of_entity": None,
            "business_management": None,
            "created_at": datetime.now().isoformat()
        }

        self._extract_from_scripts(soup, data)
        self._extract_from_html(soup, data)
        self._clean_data(data)

        return data

    def _extract_from_scripts(self, soup: BeautifulSoup, data: Dict[str, Any]):
        script_tag = soup.find('script', string=re.compile(r'var webDigitalData\s*='))
        if script_tag:
            patterns = [
                r'var webDigitalData\s*=\s*({.*?});',
                r'var webDigitalData\s*=\s*({.*?})(?=</script>)',
                r'var webDigitalData\s*=\s*({.*?})(?=\s*</script>)',
                r'var webDigitalData\s*=\s*({.*})'
            ]

            for pattern in patterns:
                match = re.search(pattern, script_tag.string, re.DOTALL)
                if match:
                    try:
                        web_data = json.loads(match.group(1))
                        business_info = web_data.get('business_info', {})

                        data['name'] = business_info.get('business_name')
                        data['phone'] = business_info.get('business_phone')

                        rating = business_info.get('business_rating')
                        data['bbb_rating'] = 'No Rating' if rating == 'NR' else rating
                        break
                    except json.JSONDecodeError:
                        continue

        json_ld_scripts = soup.find_all('script', type='application/ld+json')
        for script in json_ld_scripts:
            if script.string:
                try:
                    ld_data = json.loads(script.string)
                    items = ld_data if isinstance(ld_data, list) else [ld_data]

                    for item in items:
                        if item.get('@type') == 'LocalBusiness':
                            if not data['name']:
                                data['name'] = item.get('name')
                            if not data['phone']:
                                data['phone'] = item.get('telephone')
                            if 'foundingDate' in item:
                                data['business_started'] = item['foundingDate']
                                data['business_started_locally'] = item['foundingDate']
                                data['date_opened'] = item['foundingDate']

                            if not data['street'] and 'address' in item:
                                addr = item['address']
                                data['street'] = addr.get('streetAddress')
                                data['city'] = addr.get('addressLocality')
                                data['state'] = addr.get('addressRegion')
                                postal_code = addr.get('postalCode')
                                if postal_code:
                                    if '-' in postal_code:
                                        zip_parts = postal_code.split('-', 1)
                                        data['zip'] = zip_parts[0]
                                        data['zipcode_extended'] = postal_code
                                    else:
                                        data['zip'] = postal_code
                                        data['zipcode_extended'] = postal_code
                except json.JSONDecodeError:
                    continue

    def _extract_from_html(self, soup: BeautifulSoup, data: Dict[str, Any]):
        if not data['name']:
            data['name'] = self._get_text(soup, ['h1[class*="business-name"]', 'h1'])
        if not data['phone']:
            data['phone'] = self._get_text(soup, ['a[href^="tel:"]'])
        if not data['bbb_rating']:
            data['bbb_rating'] = self._get_text(soup, ['span[class*="rating"]', 'dt.mr-2 + dd'])

        if not data['street']:
            address_text = self._get_text(soup, ['div[class*="address"] p', 'address', 'div[class*="location"]', '.address-line'], separator=' ')
            if address_text:
                self._parse_address(address_text, data)

        business_details = {}
        details_container = soup.find(lambda tag: tag.name in ['h2', 'h3'] and 'Business Details' in tag.text)
        if details_container:
            parent_section = details_container.find_parent()
            for dt in parent_section.find_all('dt'):
                key = dt.get_text(strip=True).replace(':', '')
                dd = dt.find_next_sibling('dd')
                if key and dd:
                    business_details[key] = dd.get_text(separator='\n', strip=True)

        if 'Business Started' in business_details and not data['business_started']:
            data['business_started'] = business_details['Business Started']
            data['business_started_locally'] = business_details['Business Started']
            data['date_opened'] = business_details['Business Started']

        if 'Business Incorporated' in business_details:
            data['business_incorporated'] = business_details['Business Incorporated']

        if 'Type of Entity' in business_details:
            data['type_of_entity'] = business_details['Type of Entity']
        elif 'Entity Type' in business_details:
            data['type_of_entity'] = business_details['Entity Type']
        elif 'Business Type' in business_details:
            data['type_of_entity'] = business_details['Business Type']

        if 'BBB File Opened' in business_details:
            data['bbb_file_opened'] = business_details['BBB File Opened']

        if 'Local BBB' in business_details:
            data['local_bbb'] = business_details['Local BBB']

        management_text = (business_details.get('Business Management') or
                          business_details.get('Principal Contacts') or
                          business_details.get('Customer Contacts'))
        if management_text:
            data['business_management'] = management_text

        self._extract_website(soup, data, business_details)
        self._extract_email(soup, data, business_details)

    def _extract_website(self, soup: BeautifulSoup, data: Dict[str, Any], business_details: Dict[str, str]):
        website_text = (business_details.get('Website') or
                       business_details.get('Web Site') or
                       business_details.get('Homepage'))

        if website_text:
            url_match = re.search(r'https?://[^\s]+', website_text)
            if url_match:
                url = url_match.group(0)
                if not any(domain in url.lower() for domain in ['bbb.org', 'bbbprograms.org', 'e2ma.net']):
                    data['website'] = url
                    return
            elif website_text.startswith('www.'):
                data['website'] = f"http://{website_text}"
                return

        visit_links = soup.find_all('a', string=re.compile(r'Visit Website', re.I))
        for link in visit_links:
            href = link.get('href', '')
            if href.startswith('http') and not any(domain in href.lower() for domain in self.skip_domains):
                data['website'] = href
                return

        for link in soup.find_all('a', href=True):
            href = link.get('href', '')
            text = link.get_text(strip=True).lower()

            if (any(domain in href.lower() for domain in self.skip_domains) or
                any(word in text for word in ['share', 'facebook', 'twitter', 'social', 'bbb', 'signup', 'subscribe'])):
                continue

            if (href.startswith('http') and any(ext in href for ext in ['.com', '.org', '.net']) and
                not any(skip_word in href.lower() for skip_word in ['signup', 'subscribe', 'newsletter', 'email'])):
                data['website'] = href
                return

    def _extract_email(self, soup: BeautifulSoup, data: Dict[str, Any], business_details: Dict[str, str]):
        email_text = (business_details.get('Email') or
                     business_details.get('E-mail') or
                     business_details.get('Contact Email'))

        if email_text:
            email_match = re.search(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', email_text)
            if email_match:
                data['email'] = email_match.group(0)
                return

        for link in soup.find_all('a', href=re.compile(r'^mailto:')):
            email_href = link.get('href', '')
            if email_href.startswith('mailto:'):
                email = email_href[7:].split('?')[0].split('&')[0]
                if re.match(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', email):
                    data['email'] = email
                    return

        contact_sections = soup.find_all(['div', 'section'], class_=re.compile(r'contact|business-info', re.I))
        for section in contact_sections:
            section_text = section.get_text()
            email_matches = re.findall(r'\b[A-Za-z0-9][A-Za-z0-9._%+-]*@[A-Za-z0-9][A-Za-z0-9.-]+\.[A-Za-z]{2,}\b', section_text)
            for email in email_matches:
                if (len(email) > 5 and email.count('@') == 1 and '.' in email.split('@')[1] and
                    not any(skip in email.lower() for skip in ['example.com', 'test.com', 'bbb.org', 'texas.gov', 'tsbde'])):
                    data['email'] = email
                    return

    def _parse_address(self, address_text: str, data: Dict[str, Any]):
        address_text = re.sub(r'\s+', ' ', address_text.strip())

        zip_match = re.search(r'(\d{5})(?:-(\d{4}))?', address_text)
        if zip_match:
            data['zip'] = zip_match.group(1)
            data['zipcode_extended'] = f"{zip_match.group(1)}-{zip_match.group(2)}" if zip_match.group(2) else zip_match.group(1)
            address_text = address_text[:zip_match.start()].strip()

        state_match = re.search(r'\b([A-Z]{2})\b', address_text)
        if state_match:
            data['state'] = state_match.group(1)
            address_text = address_text[:state_match.start()].strip()

        parts = [part.strip() for part in address_text.split(',')]
        if len(parts) >= 2:
            data['street'] = parts[0]
            data['city'] = parts[-1]
        elif len(parts) == 1:
            data['street'] = parts[0]

    def _clean_data(self, data: Dict[str, Any]):
        if data.get('name'):
            data['name'] = data['name'].split('|')[0].strip()

        if data.get('phone'):
            match = re.search(r'\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}', data['phone'])
            if match:
                data['phone'] = match.group(0)

        if data.get('email'):
            data['email'] = data['email'].strip().lower()

        if data.get('website'):
            website = data['website'].strip()
            if not website.startswith(('http://', 'https://')):
                if website.startswith('www.') or '.' in website:
                    website = f"http://{website}"
            data['website'] = website

        for field in ['street', 'city', 'state', 'zip', 'zipcode_extended']:
            if data.get(field):
                data[field] = str(data[field]).strip() if data[field] else None

    def _get_text(self, soup: BeautifulSoup, selectors, separator: str = '') -> Optional[str]:
        for selector in selectors:
            element = soup.select_one(selector)
            if element:
                return element.get_text(separator=separator, strip=True)
        return None

def process_single_file(file_path: Path) -> Optional[Dict[str, Any]]:
    """
    Worker function to read and extract data from a single HTML file.
    Designed to be run in a separate process.
    
    Args:
        file_path: The path object of the HTML file to process.

    Returns:
        A dictionary of the extracted business data, or None if an error occurs.
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        extractor = BBBProfileExtractor()
        business_data = extractor.extract_profile(html_content)
        business_data['source_file'] = file_path.name
        return business_data
    except Exception as e:
        print(f"Error processing file {file_path.name}: {e}")
        return None

def process_directory_in_parallel(input_dir: str, output_file: str) -> None:
    """
    Finds all HTML files in a directory, processes them in parallel using a
    process pool, and saves the consolidated results to a single JSON file.

    Args:
        input_dir: Path to the directory containing HTML files.
        output_file: Path to save the consolidated JSON data.
    """
    input_path = Path(input_dir)
    if not input_path.is_dir():
        raise FileNotFoundError(f"Input directory not found: {input_dir}")

    html_files = list(input_path.glob("*.html"))
    if not html_files:
        print(f"No HTML files found in directory: {input_dir}")
        return

    print(f"Found {len(html_files)} HTML files to process...")
    all_business_data = []
    
    # Use ProcessPoolExecutor to leverage multiple CPU cores. It will default to the number of processors on the machine.
    with ProcessPoolExecutor() as executor:
        future_to_file = {executor.submit(process_single_file, file): file for file in html_files}
        
        print("Processing files in parallel...")
        for i, future in enumerate(as_completed(future_to_file)):
            file_path = future_to_file[future]
            try:
                result = future.result()
                if result:
                    all_business_data.append(result)
                print(f"  Processed {i + 1}/{len(html_files)}: {file_path.name}", end='\r')
            except Exception as exc:
                print(f"\n{file_path.name} generated an exception: {exc}")

    print("\n\nAll files processed.")

    if not all_business_data:
        print("No data was successfully extracted.")
        return

    # Write the consolidated data to the output file
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(all_business_data, f, indent=4, ensure_ascii=False)
        print(f"\n✅ Data from {len(all_business_data)} files successfully extracted and saved to {output_file}")
    except IOError as e:
        print(f"\n❌ Error writing to output file {output_file}: {e}")


def main():
    """Main function to run the parallel extraction process."""
    start_time = time.time()
    try:
        # Directory containing potentially thousands of HTML files
        input_html_directory = r"C:\Users\<USER>\Downloads\flat\flat"
        output_json_path = 'data/ALL_dental_practices.json'
        
        process_directory_in_parallel(input_html_directory, output_json_path)

    except FileNotFoundError as e:
        print(f"Error: {e}. Please ensure the input directory exists.")
    except Exception as e:
        print(f"An unexpected error occurred in main: {e}")
    finally:
        end_time = time.time()
        print(f"\nTotal execution time: {end_time - start_time:.2f} seconds.")

if __name__ == "__main__":
    main()