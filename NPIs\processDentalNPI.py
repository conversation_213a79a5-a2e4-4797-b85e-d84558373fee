import gc
import os
import csv
import json
import time
from pathlib import Path
from typing import Dict, List, Set, Any
from dataclasses import dataclass

@dataclass
class FileConfig:
   main_file: str
   other_name_file: str
   practice_location_file: str
   endpoint_file: str
   type1_output: str
   type2_output: str

class ProgressTracker:
   def __init__(self, total_size: int = 0):
       self.total_size = total_size
       self.processed_records = 0
       self.type1_count = 0
       self.type2_count = 0
       self.dental_matches = 0
       self.start_time = time.time()
       self.last_update = time.time()
       
   def update(self, records_processed: int, type1_added: int = 0, type2_added: int = 0, dental_found: int = 0):
       self.processed_records += records_processed
       self.type1_count += type1_added
       self.type2_count += type2_added
       self.dental_matches += dental_found
       
       current_time = time.time()
       if current_time - self.last_update >= 10:
           self._print_progress()
           self.last_update = current_time
   
   def _print_progress(self):
       elapsed = time.time() - self.start_time
       if elapsed > 0:
           rate = self.processed_records / elapsed
           eta = (self.total_size - self.processed_records) / rate if rate > 0 and self.total_size > 0 else 0
           
           print(f"\rProcessed: {self.processed_records:,} records | "
                 f"Rate: {rate:.0f} rec/sec | "
                 f"Type1: {self.type1_count:,} | "
                 f"Type2: {self.type2_count:,} | "
                 f"Dental: {self.dental_matches:,} | "
                 f"Elapsed: {elapsed:.1f}s | "
                 f"ETA: {eta:.1f}s", end='', flush=True)
   
   def final_summary(self):
       elapsed = time.time() - self.start_time
       print(f"\n\nFinal Summary:")
       print(f"Total records processed: {self.processed_records:,}")
       print(f"Type 1 dental providers: {self.type1_count:,}")
       print(f"Type 2 dental organizations: {self.type2_count:,}")
       print(f"Total dental matches: {self.dental_matches:,}")
       print(f"Processing time: {elapsed:.1f} seconds")
       print(f"Average rate: {self.processed_records/elapsed:.0f} records/second")

class MemoryEfficientNPIProcessor:
   def __init__(self, dental_taxonomy_codes: Set[str], chunk_size: int = 10000):
       self.dental_taxonomy_codes = dental_taxonomy_codes
       self.chunk_size = chunk_size
       self.type1_records = []
       self.type2_records = []
       self.type1_buffer_size = 0
       self.type2_buffer_size = 0
       self.max_buffer_size = 50 * 1024 * 1024
       self.progress = None
       
   def _estimate_file_size(self, filepath: str) -> int:
       try:
           with open(filepath, 'r', encoding='utf-8') as f:
               first_line = f.readline()
               if not first_line:
                   return 0
               
               sample_size = min(1000000, os.path.getsize(filepath))
               f.seek(0)
               sample = f.read(sample_size)
               line_count = sample.count('\n')
               
               file_size = os.path.getsize(filepath)
               estimated_lines = int((file_size / sample_size) * line_count) - 1
               
               return max(estimated_lines, 0)
       except:
           return 0
   
   def _load_reference_data(self, config: FileConfig) -> tuple:
       print("Loading reference data...")
       other_names = {}
       practice_locations = {}
       endpoints = {}
       
       if Path(config.other_name_file).exists():
           print("Loading other names...")
           with open(config.other_name_file, 'r', encoding='utf-8') as f:
               reader = csv.DictReader(f)
               for row in reader:
                   npi = row.get('NPI', '').strip()
                   if npi:
                       other_names.setdefault(npi, []).append({
                           'name': row.get('Provider Other Organization Name', ''),
                           'type': row.get('Provider Other Organization Name Type Code', '')
                       })
       
       if Path(config.practice_location_file).exists():
           print("Loading practice locations...")
           with open(config.practice_location_file, 'r', encoding='utf-8') as f:
               reader = csv.DictReader(f)
               for row in reader:
                   npi = row.get('NPI', '').strip()
                   if npi:
                       practice_locations.setdefault(npi, []).append({
                           'addr1': row.get('Provider Secondary Practice Location Address- Address Line 1', ''),
                           'addr2': row.get('Provider Secondary Practice Location Address-  Address Line 2', ''),
                           'city': row.get('Provider Secondary Practice Location Address - City Name', ''),
                           'state': row.get('Provider Secondary Practice Location Address - State Name', ''),
                           'zip': row.get('Provider Secondary Practice Location Address - Postal Code', ''),
                           'country': row.get('Provider Secondary Practice Location Address - Country Code (If outside U.S.)', ''),
                           'phone': row.get('Provider Secondary Practice Location Address - Telephone Number', ''),
                           'ext': row.get('Provider Secondary Practice Location Address - Telephone Extension', '')
                       })
       
       if Path(config.endpoint_file).exists():
           print("Loading endpoints...")
           with open(config.endpoint_file, 'r', encoding='utf-8') as f:
               reader = csv.DictReader(f)
               for row in reader:
                   npi = row.get('NPI', '').strip()
                   if npi:
                       endpoints.setdefault(npi, []).append({
                           'type': row.get('Endpoint Type', ''),
                           'address': row.get('Endpoint', ''),
                           'use_code': row.get('Use Code', ''),
                           'content_type': row.get('Content Type', ''),
                           'affiliation': row.get('Affiliation Legal Business Name', '')
                       })
       
       print(f"Reference data loaded: {len(other_names)} other names, {len(practice_locations)} locations, {len(endpoints)} endpoints")
       return other_names, practice_locations, endpoints
   
   def _extract_taxonomies(self, row: Dict[str, str]) -> List[Dict[str, Any]]:
       taxonomies = []
       for i in range(1, 16):
           code = row.get(f'Healthcare Provider Taxonomy Code_{i}', '').strip()
           if code in self.dental_taxonomy_codes:
               taxonomies.append({
                   'code': code,
                   'license': row.get(f'Provider License Number_{i}', ''),
                   'license_state': row.get(f'Provider License Number State Code {i}', ''),
                   'primary': row.get(f'Healthcare Provider Primary Taxonomy Switch_{i}', '') == 'Y'
               })
       return taxonomies
   
   def _create_type1_record(self, row: Dict[str, str], taxonomies: List[Dict], 
                          locations: List[Dict], endpoints: List[Dict]) -> Dict[str, Any]:
       return {
           'npi': row.get('NPI', ''),
           'last_name': row.get('Provider Last Name (Legal Name)', ''),
           'first_name': row.get('Provider First Name', ''),
           'credentials': row.get('Provider Credential Text', ''),
           'address': row.get('Provider First Line Business Practice Location Address', ''),
           'city': row.get('Provider Business Practice Location Address City Name', ''),
           'state': row.get('Provider Business Practice Location Address State Name', ''),
           'zip_code': row.get('Provider Business Practice Location Address Postal Code', ''),
           'phone': row.get('Provider Business Practice Location Address Telephone Number', ''),
           'enum_date': row.get('Provider Enumeration Date', ''),
           'update_date': row.get('Last Update Date', ''),
           'gender': row.get('Provider Sex Code', ''),
           'taxonomies': [{'code': t['code'], 'license': t['license'], 'primary': t['primary']} for t in taxonomies],
           'locations': locations,
           'endpoints': endpoints
       }
   
   def _create_type2_record(self, row: Dict[str, str], taxonomies: List[Dict], 
                          other_names: List[Dict], locations: List[Dict], endpoints: List[Dict]) -> Dict[str, Any]:
       return {
           'npi': row.get('NPI', ''),
           'ein': row.get('Employer Identification Number (EIN)', ''),
           'org_name': row.get('Provider Organization Name (Legal Business Name)', ''),
           'address': row.get('Provider First Line Business Practice Location Address', ''),
           'city': row.get('Provider Business Practice Location Address City Name', ''),
           'state': row.get('Provider Business Practice Location Address State Name', ''),
           'zip_code': row.get('Provider Business Practice Location Address Postal Code', ''),
           'phone': row.get('Provider Business Practice Location Address Telephone Number', ''),
           'fax': row.get('Provider Business Practice Location Address Fax Number', ''),
           'enum_date': row.get('Provider Enumeration Date', ''),
           'update_date': row.get('Last Update Date', ''),
           'sole_proprietor': row.get('Is Sole Proprietor', '') == 'Y',
           'org_subpart': row.get('Is Organization Subpart', '') == 'Y',
           'parent_org': row.get('Parent Organization LBN', ''),
           'auth_official': {
               'last_name': row.get('Authorized Official Last Name', ''),
               'first_name': row.get('Authorized Official First Name', ''),
               'title': row.get('Authorized Official Title or Position', ''),
               'phone': row.get('Authorized Official Telephone Number', ''),
               'credential': row.get('Authorized Official Credential Text', '')
           },
           'taxonomies': taxonomies,
           'other_names': other_names,
           'locations': locations,
           'endpoints': endpoints
       }
   
   def _flush_buffers(self, config: FileConfig, force: bool = False):
       if self.type1_records and (self.type1_buffer_size >= self.max_buffer_size or force):
           mode = 'a' if Path(config.type1_output).exists() else 'w'
           with open(config.type1_output, mode, encoding='utf-8') as f:
               if mode == 'w':
                   f.write('[\n')
               else:
                   f.seek(f.tell() - 2)
                   f.write(',\n')
               for i, record in enumerate(self.type1_records):
                   json.dump(record, f, separators=(',', ':'))
                   if i < len(self.type1_records) - 1:
                       f.write(',\n')
                   else:
                       f.write('\n')
               f.write(']')
           self.type1_records.clear()
           self.type1_buffer_size = 0
           
       if self.type2_records and (self.type2_buffer_size >= self.max_buffer_size or force):
           mode = 'a' if Path(config.type2_output).exists() else 'w'
           with open(config.type2_output, mode, encoding='utf-8') as f:
               if mode == 'w':
                   f.write('[\n')
               else:
                   f.seek(f.tell() - 2)
                   f.write(',\n')
               for i, record in enumerate(self.type2_records):
                   json.dump(record, f, separators=(',', ':'))
                   if i < len(self.type2_records) - 1:
                       f.write(',\n')
                   else:
                       f.write('\n')
               f.write(']')
           self.type2_records.clear()
           self.type2_buffer_size = 0
       
       gc.collect()
   
   def _finalize_json_files(self, config: FileConfig):
       for filepath in [config.type1_output, config.type2_output]:
           if Path(filepath).exists():
               with open(filepath, 'r+', encoding='utf-8') as f:
                   content = f.read()
                   if content.endswith(',\n]'):
                       f.seek(f.tell() - 3)
                       f.write('\n]')
                       f.truncate()
   
   def process_large_npi_file(self, config: FileConfig):
       if Path(config.type1_output).exists():
           os.remove(config.type1_output)
       if Path(config.type2_output).exists():
           os.remove(config.type2_output)
       
       print("Estimating file size...")
       estimated_records = self._estimate_file_size(config.main_file)
       print(f"Estimated records in main file: {estimated_records:,}")
       
       self.progress = ProgressTracker(estimated_records)
       
       other_names, practice_locations, endpoints = self._load_reference_data(config)
       
       print(f"\nStarting main file processing...")
       print(f"Progress updates every 10 seconds...")
       
       batch_processed = 0
       batch_type1 = 0
       batch_type2 = 0
       batch_dental = 0
       
       with open(config.main_file, 'r', encoding='utf-8') as f:
           reader = csv.DictReader(f)
           
           for row in reader:
               batch_processed += 1
               
               entity_type = row.get('Entity Type Code', '').strip()
               taxonomies = self._extract_taxonomies(row)
               
               if taxonomies:
                   batch_dental += 1
                   npi = row.get('NPI', '').strip()
                   record_locations = practice_locations.get(npi, [])
                   record_endpoints = endpoints.get(npi, [])
                   
                   if entity_type == '1':
                       record = self._create_type1_record(row, taxonomies, record_locations, record_endpoints)
                       self.type1_records.append(record)
                       self.type1_buffer_size += len(json.dumps(record, separators=(',', ':')))
                       batch_type1 += 1
                       
                   elif entity_type == '2':
                       record_other_names = other_names.get(npi, [])
                       record = self._create_type2_record(row, taxonomies, record_other_names, record_locations, record_endpoints)
                       self.type2_records.append(record)
                       self.type2_buffer_size += len(json.dumps(record, separators=(',', ':')))
                       batch_type2 += 1
               
               if batch_processed % self.chunk_size == 0:
                   self._flush_buffers(config)
                   self.progress.update(batch_processed, batch_type1, batch_type2, batch_dental)
                   batch_processed = 0
                   batch_type1 = 0
                   batch_type2 = 0
                   batch_dental = 0
       
       if batch_processed > 0:
           self.progress.update(batch_processed, batch_type1, batch_type2, batch_dental)
       
       self._flush_buffers(config, force=True)
       self._finalize_json_files(config)
       
       self.progress.final_summary()
       
       print(f"\nOutput files created:")
       print(f"Type 1: {config.type1_output}")
       print(f"Type 2: {config.type2_output}")

def main():
   dental_taxonomy_codes = {
       "125K00000X", "126800000X", "124Q00000X", "126900000X", "125J00000X",
       "122300000X", "1223D0004X", "1223D0001X", "1223E0200X", "1223G0001X",
       "1223P0106X", "1223X0008X", "1223S0112X", "125Q00000X", "1223X2210X",
       "1223X0400X", "1223P0221X", "1223P0300X", "1223P0700X", "122400000X"
   }
   
   config = FileConfig(
       main_file="NPI-weekly-data/main.csv",
       other_name_file="NPI-weekly-data/otherName.csv",
       practice_location_file="NPI-weekly-data/pracLoc.csv",
       endpoint_file="NPI-weekly-data/endpoint.csv",
       type1_output="dentists_type1_npi_data.json",
       type2_output="dentists_type2_npi_data.json"
   )
   
   processor = MemoryEfficientNPIProcessor(dental_taxonomy_codes, chunk_size=5000)
   processor.process_large_npi_file(config)

if __name__ == "__main__":
   main()