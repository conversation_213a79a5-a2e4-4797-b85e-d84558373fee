import requests
import json
from lxml import etree
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import datetime

def fetch_sitemap_content(url):
    """
    Fetches the content of a given URL.
    Returns the content as text or None if an error occurs.
    """
    try:
        response = requests.get(url, timeout=30)
        response.raise_for_status()  # Raise an exception for bad status codes (4xx or 5xx)
        return response.content
    except requests.exceptions.RequestException as e:
        print(f"Error fetching {url}: {e}")
        return None

def parse_sitemap(xml_content):
    """
    Parses the XML content of a sitemap and extracts URLs and last modification dates.
    Uses lxml for fast parsing.
    """
    if not xml_content:
        return []

    urls = []
    # The {http://www.sitemaps.org/schemas/sitemap/0.9} is the namespace
    namespace = '{http://www.sitemaps.org/schemas/sitemap/0.9}'
    try:
        # We use fromstring which is faster for in-memory content
        root = etree.fromstring(xml_content)
        for url_element in root.findall(f'{namespace}url'):
            loc = url_element.findtext(f'{namespace}loc')
            lastmod = url_element.findtext(f'{namespace}lastmod')
            if loc:
                urls.append({"loc": loc, "lastmod": lastmod})
    except etree.XMLSyntaxError as e:
        print(f"Error parsing XML: {e}")
    return urls

def fetch_and_parse_sitemap(sitemap_url):
    """
    A worker function that fetches and parses a single sitemap.
    """
    print(f"Processing sitemap: {sitemap_url}")
    xml_content = fetch_sitemap_content(sitemap_url)
    urls = parse_sitemap(xml_content)
    print(f"Finished processing {sitemap_url}, found {len(urls)} URLs.")
    return urls

def main():
    """
    Main function to orchestrate the fetching and parsing of sitemaps.
    """
    sitemap_index_url = "https://www.carecredit.com/assets/sitemaps/sitemap_doctors_index.xml"
    print(f"Fetching sitemap index from: {sitemap_index_url}")

    # 1. Fetch the main sitemap index
    index_content = fetch_sitemap_content(sitemap_index_url)
    if not index_content:
        print("Could not fetch the sitemap index. Exiting.")
        return

    # 2. Parse the sitemap index to get individual sitemap URLs
    sitemap_urls = []
    namespace = '{http://www.sitemaps.org/schemas/sitemap/0.9}'
    try:
        root = etree.fromstring(index_content)
        # We are looking for the sitemaps that contain doctor information
        for sitemap_element in root.findall(f'{namespace}sitemap'):
            loc = sitemap_element.findtext(f'{namespace}loc')
            if loc and 'sitemap_doctors' in loc:
                sitemap_urls.append(loc)
    except etree.XMLSyntaxError as e:
        print(f"Error parsing sitemap index: {e}")
        return

    if not sitemap_urls:
        print("No doctor sitemap URLs found in the index. Exiting.")
        return

    print(f"Found {len(sitemap_urls)} doctor sitemaps to process.")

    # 3. Use a ThreadPoolExecutor to fetch and parse in parallel
    all_urls = []
    # Using max_workers=10 to not overwhelm the server
    with ThreadPoolExecutor(max_workers=10) as executor:
        # Submit all sitemap URLs to the executor
        future_to_url = {executor.submit(fetch_and_parse_sitemap, url): url for url in sitemap_urls}

        for future in as_completed(future_to_url):
            try:
                # Get the result (list of URLs) from the completed future
                urls = future.result()
                if urls:
                    all_urls.extend(urls)
            except Exception as exc:
                print(f"A sitemap generated an exception: {exc}")

    # 4. Structure the final JSON output
    output_data = {
        "generated_at": datetime.now().isoformat(),
        "sitemaps": all_urls
    }

    # 5. Write the output to a JSON file
    output_filename = "carecredit_doctor_urls.json"
    try:
        with open(output_filename, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, indent=2, ensure_ascii=False)
        print(f"\nSuccessfully extracted {len(all_urls)} URLs.")
        print(f"Data saved to {output_filename}")
    except IOError as e:
        print(f"Error writing to file {output_filename}: {e}")

if __name__ == "__main__":
    main()