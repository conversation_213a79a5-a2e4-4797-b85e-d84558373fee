import json
import re
from typing import Dict, Any, <PERSON>, <PERSON><PERSON>
from difflib import SequenceMatcher
from urllib.parse import urlparse

class ParentChildLinker:
    def __init__(self, threshold: float = 0.8):
        self.threshold = threshold
        self.unmatched_id = "UNMATCHED_DSO"
    def process_extracted_data(self, extracted_results: List[Dict[str, Any]]) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
        dsos = []
        branches = []
        independent_practices = []

        for result in extracted_results:
            if result['type'] == 'DSO_HQ':
                dsos.append(result['data'])
            elif result['type'] == 'DSO_BRANCH':
                branches.append(result['data'])
            elif result['type'] == 'DDS':
                independent_practices.append(result['data'])

        dso_lookup = {self._normalize_name(dso['name']): dso for dso in dsos if dso.get('name')}
        matched_branches, unmatched_branches = self._match_branches(branches, dso_lookup)

        all_practices = independent_practices + matched_branches + unmatched_branches

        print(f"DSO HQ: {len(dsos)}, Matched: {len(matched_branches)}, Unmatched: {len(unmatched_branches)}, Independent: {len(independent_practices)}")

        return dsos, all_practices
    def _normalize_name(self, name: str) -> str:
        if not name:
            return ""
        normalized = name.lower().strip()
        patterns = [r'\b(llc|inc|corp|dental|dentistry|group|associates|practice|clinic|the|a|an)\b']
        for pattern in patterns:
            normalized = re.sub(pattern, '', normalized)
        return re.sub(r'\s+', ' ', re.sub(r'[^\w\s]', '', normalized)).strip()

    def _calculate_similarity(self, name1: str, name2: str) -> float:
        norm1 = self._normalize_name(name1)
        norm2 = self._normalize_name(name2)
        return SequenceMatcher(None, norm1, norm2).ratio() if norm1 and norm2 else 0.0
    def _match_branches(self, branches: List[Dict[str, Any]], dso_lookup: Dict[str, Dict[str, Any]]) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
        matched = []
        unmatched = []

        for branch in branches:
            branch_name = branch.get('name', '')
            normalized_branch = self._normalize_name(branch_name)

            best_match = None
            best_score = 0.0

            if normalized_branch in dso_lookup:
                best_match = dso_lookup[normalized_branch]
                best_score = 1.0
            else:
                for dso in dso_lookup.values():
                    score = self._calculate_similarity(branch_name, dso.get('name', ''))
                    if branch.get('phone') and dso.get('phone') and self._phones_match(branch['phone'], dso['phone']):
                        score += 0.3
                    if branch.get('website') and dso.get('website') and self._websites_match(branch['website'], dso['website']):
                        score += 0.2
                    if score > best_score:
                        best_score = score
                        best_match = dso

            if best_match and best_score >= self.threshold:
                branch['dso_id'] = best_match['id']
                matched.append(branch)
                print(f"✅ Matched '{branch_name}' to '{best_match.get('name')}' ({best_score:.2f})")
            else:
                branch['dso_id'] = self.unmatched_id
                unmatched.append(branch)
                print(f"❌ No match for '{branch_name}'")

        return matched, unmatched

    def _phones_match(self, phone1: str, phone2: str) -> bool:
        if not phone1 or not phone2:
            return False
        digits1 = re.sub(r'\D', '', phone1)
        digits2 = re.sub(r'\D', '', phone2)
        return digits1 == digits2 and len(digits1) >= 10

    def _websites_match(self, website1: str, website2: str) -> bool:
        if not website1 or not website2:
            return False
        try:
            domain1 = urlparse(website1.lower()).netloc.replace('www.', '')
            domain2 = urlparse(website2.lower()).netloc.replace('www.', '')
            return domain1 == domain2 and domain1 != ''
        except:
            return False
    def save_results(self, dsos: List[Dict[str, Any]], practices: List[Dict[str, Any]]):
        with open("dsos.json", 'w', encoding='utf-8') as f:
            json.dump(dsos, f, indent=4, ensure_ascii=False)
        with open("dentalpractices.json", 'w', encoding='utf-8') as f:
            json.dump(practices, f, indent=4, ensure_ascii=False)

        matched = sum(1 for p in practices if p.get('dso_id') and p['dso_id'] != self.unmatched_id and p['dso_id'] is not None)
        unmatched = sum(1 for p in practices if p.get('dso_id') == self.unmatched_id)
        independent = sum(1 for p in practices if p.get('dso_id') is None)

        print(f"� Saved {len(dsos)} DSOs and {len(practices)} practices")
        print(f"📊 Independent: {independent}, Matched: {matched}, Unmatched: {unmatched}")

        with open("processing_report.txt", "w") as f:
            f.write(f"DSOs: {len(dsos)}\nPractices: {len(practices)}\nIndependent: {independent}\nMatched: {matched}\nUnmatched: {unmatched}")
        print("📄 Report saved to processing_report.txt")