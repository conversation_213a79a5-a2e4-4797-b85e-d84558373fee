import csv
import json
import logging
from typing import Dict, List, Set
from pathlib import Path


class NPIDataProcessor:
   def __init__(self, dental_taxonomy_codes: Set[str]):
       self.dental_taxonomy_codes = dental_taxonomy_codes
       self.logger = self._setup_logging()
       
   def _setup_logging(self) -> logging.Logger:
       logging.basicConfig(
           level=logging.INFO,
           format='%(asctime)s - %(levelname)s - %(message)s'
       )
       return logging.getLogger(__name__)
       
   def _validate_file_exists(self, filepath: str) -> bool:
       return Path(filepath).exists()
       
   def _safe_get_column_value(self, row: List[str], indices: Dict[str, int], 
                             column_name: str) -> str:
       index = indices.get(column_name, -1)
       return row[index].strip() if 0 <= index < len(row) else ""
       
   def _load_other_names(self, filepath: str) -> Dict[str, List[Dict[str, str]]]:
       other_names = {}
       
       if not self._validate_file_exists(filepath):
           self.logger.warning(f"Other names file not found: {filepath}")
           return other_names
           
       try:
           with open(filepath, 'r', encoding='utf-8') as f:
               reader = csv.reader(f)
               header = [col.strip() for col in next(reader)]
               
               indices = {}
               for col in ["NPI", "Provider Other Organization Name", 
                          "Provider Other Organization Name Type Code"]:
                   if col in header:
                       indices[col] = header.index(col)
                       
               if "NPI" not in indices:
                   self.logger.error("NPI column not found in other names file")
                   return other_names
                   
               for row in reader:
                   if len(row) <= indices["NPI"]:
                       continue
                       
                   npi = row[indices["NPI"]].strip()
                   if not npi:
                       continue
                       
                   name_data = {
                       "name": self._safe_get_column_value(row, indices, "Provider Other Organization Name"),
                       "type": self._safe_get_column_value(row, indices, "Provider Other Organization Name Type Code")
                   }
                   
                   other_names.setdefault(npi, []).append(name_data)
                   
       except Exception as e:
           self.logger.error(f"Error loading other names file: {e}")
           
       return other_names
       
   def _load_practice_locations(self, filepath: str) -> Dict[str, List[Dict[str, str]]]:
       practice_locations = {}
       
       if not self._validate_file_exists(filepath):
           self.logger.warning(f"Practice locations file not found: {filepath}")
           return practice_locations
           
       try:
           with open(filepath, 'r', encoding='utf-8') as f:
               reader = csv.reader(f)
               header = [col.strip() for col in next(reader)]
               
               indices = {}
               columns = [
                   "NPI",
                   "Provider Secondary Practice Location Address- Address Line 1",
                   "Provider Secondary Practice Location Address-  Address Line 2",
                   "Provider Secondary Practice Location Address - City Name",
                   "Provider Secondary Practice Location Address - State Name",
                   "Provider Secondary Practice Location Address - Postal Code",
                   "Provider Secondary Practice Location Address - Country Code (If outside U.S.)",
                   "Provider Secondary Practice Location Address - Telephone Number",
                   "Provider Secondary Practice Location Address - Telephone Extension",
                   "Provider Practice Location Address - Fax Number"
               ]
               
               for col in columns:
                   if col in header:
                       indices[col] = header.index(col)
                       
               if "NPI" not in indices:
                   self.logger.error("NPI column not found in practice locations file")
                   return practice_locations
                   
               for row in reader:
                   if len(row) <= indices["NPI"]:
                       continue
                       
                   npi = row[indices["NPI"]].strip()
                   if not npi:
                       continue
                       
                   location_data = {
                       "address1": self._safe_get_column_value(row, indices, "Provider Secondary Practice Location Address- Address Line 1"),
                       "address2": self._safe_get_column_value(row, indices, "Provider Secondary Practice Location Address-  Address Line 2"),
                       "city": self._safe_get_column_value(row, indices, "Provider Secondary Practice Location Address - City Name"),
                       "state": self._safe_get_column_value(row, indices, "Provider Secondary Practice Location Address - State Name"),
                       "zip": self._safe_get_column_value(row, indices, "Provider Secondary Practice Location Address - Postal Code"),
                       "country": self._safe_get_column_value(row, indices, "Provider Secondary Practice Location Address - Country Code (If outside U.S.)"),
                       "phone": self._safe_get_column_value(row, indices, "Provider Secondary Practice Location Address - Telephone Number"),
                       "phone_ext": self._safe_get_column_value(row, indices, "Provider Secondary Practice Location Address - Telephone Extension"),
                       "fax": self._safe_get_column_value(row, indices, "Provider Practice Location Address - Fax Number")
                   }
                   
                   practice_locations.setdefault(npi, []).append(location_data)
                   
       except Exception as e:
           self.logger.error(f"Error loading practice locations file: {e}")
           
       return practice_locations
       
   def _load_endpoints(self, filepath: str) -> Dict[str, List[Dict[str, str]]]:
       endpoints = {}
       
       if not self._validate_file_exists(filepath):
           self.logger.warning(f"Endpoints file not found: {filepath}")
           return endpoints
           
       try:
           with open(filepath, 'r', encoding='utf-8') as f:
               reader = csv.reader(f)
               header = [col.strip() for col in next(reader)]
               
               indices = {}
               columns = [
                   "NPI", "Endpoint Type", "Endpoint", "Use Code", "Content Type",
                   "Affiliation Legal Business Name", "Endpoint Type Description",
                   "Use Description", "Content Description"
               ]
               
               for col in columns:
                   if col in header:
                       indices[col] = header.index(col)
                       
               if "NPI" not in indices:
                   self.logger.error("NPI column not found in endpoints file")
                   return endpoints
                   
               for row in reader:
                   if len(row) <= indices["NPI"]:
                       continue
                       
                   npi = row[indices["NPI"]].strip()
                   if not npi:
                       continue
                       
                   endpoint_data = {
                       "type": self._safe_get_column_value(row, indices, "Endpoint Type"),
                       "type_desc": self._safe_get_column_value(row, indices, "Endpoint Type Description"),
                       "address": self._safe_get_column_value(row, indices, "Endpoint"),
                       "use_code": self._safe_get_column_value(row, indices, "Use Code"),
                       "use_desc": self._safe_get_column_value(row, indices, "Use Description"),
                       "content_type": self._safe_get_column_value(row, indices, "Content Type"),
                       "content_desc": self._safe_get_column_value(row, indices, "Content Description"),
                       "affiliation_lbn": self._safe_get_column_value(row, indices, "Affiliation Legal Business Name")
                   }
                   
                   endpoints.setdefault(npi, []).append(endpoint_data)
                   
       except Exception as e:
           self.logger.error(f"Error loading endpoints file: {e}")
           
       return endpoints
       
   def _get_main_file_column_map(self) -> Dict[str, str]:
       return {
           "NPI": "NPI",
           "Employer Identification Number (EIN)": "EIN",
           "Provider Organization Name (Legal Business Name)": "OrgName",
           "Provider First Line Business Practice Location Address": "PracticeAddress1",
           "Provider Business Practice Location Address City Name": "PracticeCity",
           "Provider Business Practice Location Address State Name": "PracticeState",
           "Provider Business Practice Location Address Postal Code": "PracticeZip",
           "Provider Business Practice Location Address Telephone Number": "PracticePhone",
           "Provider Business Practice Location Address Fax Number": "PracticeFax",
           "Provider First Line Business Mailing Address": "MailingAddress1",
           "Provider Business Mailing Address City Name": "MailingCity",
           "Provider Business Mailing Address State Name": "MailingState",
           "Provider Business Mailing Address Postal Code": "MailingZip",
           "Provider Business Mailing Address Telephone Number": "MailingPhone",
           "Provider Business Mailing Address Fax Number": "MailingFax",
           "Provider Enumeration Date": "NPI_EnrollDate",
           "Last Update Date": "LastUpdateDate",
           "Is Sole Proprietor": "IsSoleProprietor",
           "Is Organization Subpart": "IsOrgSubpart",
           "Parent Organization LBN": "ParentOrgName",
           "Parent Organization TIN": "ParentOrgEIN",
           "Authorized Official Last Name": "AuthOfficialLastName",
           "Authorized Official First Name": "AuthOfficialFirstName",
           "Authorized Official Middle Name": "AuthOfficialMiddleName",
           "Authorized Official Title or Position": "AuthOfficialTitle",
           "Authorized Official Telephone Number": "AuthOfficialPhone",
           "Authorized Official Credential Text": "AuthOfficialCredential"
       }
       
   def _extract_dental_taxonomies(self, row: List[str], indices: Dict[str, int]) -> List[Dict[str, str]]:
       dental_taxonomies = []
       
       for i in range(1, 16):
           tax_col = f"Healthcare Provider Taxonomy Code_{i}"
           lic_col = f"Provider License Number_{i}"
           lic_state_col = f"Provider License Number State Code {i}"
           primary_col = f"Healthcare Provider Primary Taxonomy Switch_{i}"
           
           taxonomy_code = self._safe_get_column_value(row, indices, tax_col)
           
           if taxonomy_code in self.dental_taxonomy_codes:
               taxonomy_info = {
                   "code": taxonomy_code,
                   "license_num": self._safe_get_column_value(row, indices, lic_col),
                   "license_state": self._safe_get_column_value(row, indices, lic_state_col),
                   "is_primary": self._safe_get_column_value(row, indices, primary_col)
               }
               dental_taxonomies.append(taxonomy_info)
               
       return dental_taxonomies
       
   def _build_column_indices(self, header: List[str]) -> Dict[str, int]:
       indices = {}
       column_map = self._get_main_file_column_map()
       
       for col in list(column_map.keys()) + ["Entity Type Code"]:
           if col in header:
               indices[col] = header.index(col)
               
       for i in range(1, 16):
           for col_type in ["Healthcare Provider Taxonomy Code_", "Provider License Number_", 
                          "Provider License Number State Code ", "Healthcare Provider Primary Taxonomy Switch_"]:
               col_name = f"{col_type}{i}"
               if col_name in header:
                   indices[col_name] = header.index(col_name)
                   
       return indices
       
   def process_npi_data(self, npi_main_filepath: str, other_name_filepath: str,
                       pl_filepath: str, ep_filepath: str, output_json_filepath: str) -> bool:
       if not self._validate_file_exists(npi_main_filepath):
           self.logger.error(f"Main NPI file not found: {npi_main_filepath}")
           return False
           
       other_names = self._load_other_names(other_name_filepath)
       practice_locations = self._load_practice_locations(pl_filepath)
       endpoints = self._load_endpoints(ep_filepath)
       
       extracted_organizations = []
       column_map = self._get_main_file_column_map()
       
       try:
           with open(npi_main_filepath, 'r', encoding='utf-8') as f:
               reader = csv.reader(f)
               header = [col.strip() for col in next(reader)]
               indices = self._build_column_indices(header)
               
               if "Entity Type Code" not in indices:
                   self.logger.error("Entity Type Code column not found in main file")
                   return False
                   
               for row in reader:
                   if len(row) <= indices["Entity Type Code"]:
                       continue
                       
                   if row[indices["Entity Type Code"]].strip() != '2':
                       continue
                       
                   dental_taxonomies = self._extract_dental_taxonomies(row, indices)
                   
                   if not dental_taxonomies:
                       continue
                       
                   org_record = {}
                   for original_col, new_col in column_map.items():
                       org_record[new_col] = self._safe_get_column_value(row, indices, original_col)
                       
                   npi = org_record.get("NPI", "")
                   org_record["dental_taxonomies"] = dental_taxonomies
                   org_record["other_names"] = other_names.get(npi, [])
                   org_record["secondary_locations"] = practice_locations.get(npi, [])
                   org_record["endpoints"] = endpoints.get(npi, [])
                   
                   extracted_organizations.append(org_record)
                   
       except Exception as e:
           self.logger.error(f"Error processing main NPI file: {e}")
           return False
           
       try:
           with open(output_json_filepath, 'w', encoding='utf-8') as outfile:
               json.dump(extracted_organizations, outfile, indent=2, ensure_ascii=False)
               
           self.logger.info(f"Successfully processed {len(extracted_organizations)} organizations to {output_json_filepath}")
           return True
           
       except Exception as e:
           self.logger.error(f"Error saving results: {e}")
           return False


def main():
   dental_taxonomy_codes = {
       "125K00000X", "126800000X", "124Q00000X", "126900000X", "125J00000X",
       "122300000X", "1223D0004X", "1223D0001X", "1223E0200X", "1223G0001X",
       "1223P0106X", "1223X0008X", "1223S0112X", "125Q00000X", "1223X2210X",
       "1223X0400X", "1223P0221X", "1223P0300X", "1223P0700X", "122400000X"
   }
   
   file_paths = {
       "main": r"C:\Users\<USER>\Downloads\NPI-weekly-data\npidata_pfile_20250602-20250608.csv",
       "other_name": r"C:\Users\<USER>\Downloads\NPI-weekly-data\othername_pfile_20250602-20250608.csv",
       "practice_location": r"C:\Users\<USER>\Downloads\NPI-weekly-data\pl_pfile_20250602-20250608.csv",
       "endpoint": r"C:\Users\<USER>\Downloads\NPI-weekly-data\endpoint_pfile_20250602-20250608.csv",
       "output": "dentists_type2_npi_data.json"
   }
   
   processor = NPIDataProcessor(dental_taxonomy_codes)
   success = processor.process_npi_data(
       file_paths["main"],
       file_paths["other_name"],
       file_paths["practice_location"],
       file_paths["endpoint"],
       file_paths["output"]
   )
   
   if not success:
       exit(1)


if __name__ == "__main__":
   main()