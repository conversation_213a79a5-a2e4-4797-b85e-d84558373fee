import requests
from typing import Any, Dict, Iterator, List, Optional

BASE_URL = "https://npiregistry.cms.hhs.gov/api/"

class NPIRegistryError(Exception):
    """Custom exception for NPI Registry API errors."""
    pass


def build_query_params(
    number: Optional[str] = None,
    enumeration_type: Optional[str] = None,
    taxonomy_description: Optional[str] = None,
    first_name: Optional[str] = None,
    last_name: Optional[str] = None,
    organization_name: Optional[str] = None,
    address_purpose: Optional[str] = None,
    city: Optional[str] = None,
    state: Optional[str] = None,
    postal_code: Optional[str] = None,
    country_code: Optional[str] = None,
    limit: int = 100,
    skip: int = 0,
    version: str = "2.1",
) -> Dict[str, Any]:
    """
    Build a parameter dictionary for querying the NPI Registry API.

    Any parameter set to None will be omitted from the final query string.

    Args:
        number: 10-digit NPI (exact match).
        enumeration_type: "NPI-1" or "NPI-2".
        taxonomy_description: e.g. "Cardiology", "Dentist", etc.
        first_name:   Provider's first name.
        last_name:    <PERSON>vider's last name.
        organization_name:  Hospital/organization name.
        address_purpose: "LOCATION" or "MAILING".
        city:         City name.
        state:        2-letter state code (e.g. "CA", "NY").
        postal_code:  5-digit or ZIP+4 postal code.
        country_code: 2-letter country code (e.g. "US").
        limit:        Maximum records per request (max=200).
        skip:         How many records to skip (for pagination).
        version:      API version (currently "2.1").

    Returns:
        A dict of all non-None parameters, ready to pass to requests.get().
    """
    params: Dict[str, Any] = {"version": version, "limit": limit, "skip": skip}

    # Only include keys that are not None:
    optional_fields = {
        "number": number,
        "enumeration_type": enumeration_type,
        "taxonomy_description": taxonomy_description,
        "first_name": first_name,
        "last_name": last_name,
        "organization_name": organization_name,
        "address_purpose": address_purpose,
        "city": city,
        "state": state,
        "postal_code": postal_code,
        "country_code": country_code,
    }
    for key, value in optional_fields.items():
        if value is not None:
            params[key] = value
    return params


def fetch_once(params: Dict[str, Any]) -> List[Dict[str, Any]]:
    """
    Perform a single API call to the NPI Registry and return the "results" array.

    Raises NPIRegistryError if the HTTP status is not 200 or if "results" is missing.
    """
    response = requests.get(BASE_URL, params=params, timeout=10)
    if response.status_code != 200:
        raise NPIRegistryError(f"HTTP {response.status_code}: {response.text}")

    payload = response.json()
    if "results" not in payload:
        raise NPIRegistryError("Unexpected API response: 'results' key not found.")

    return payload["results"]  # type: ignore


def fetch_paginated(
    *,
    # Any of the same fields as build_query_params
    number: Optional[str] = None,
    enumeration_type: Optional[str] = None,
    taxonomy_description: Optional[str] = None,
    first_name: Optional[str] = None,
    last_name: Optional[str] = None,
    organization_name: Optional[str] = None,
    address_purpose: Optional[str] = None,
    city: Optional[str] = None,
    state: Optional[str] = None,
    postal_code: Optional[str] = None,
    country_code: Optional[str] = None,
    version: str = "2.1",
    batch_size: int = 200,
    max_records: int = 1200,
) -> Iterator[Dict[str, Any]]:
    """
    Generator that yields provider records from the NPI Registry API in pages.

    - batch_size: how many records per request (max=200).  
    - max_records: the overall limit you want (the API will only ever return at most 1200).

    Yields:
        One provider record (as a dict) per iteration, until no more are returned
        or max_records is reached.
    """
    if batch_size < 1 or batch_size > 200:
        raise ValueError("batch_size must be between 1 and 200")

    total_retrieved = 0
    skip = 0

    while total_retrieved < max_records:
        remaining = max_records - total_retrieved
        this_batch = min(batch_size, remaining)

        params = build_query_params(
            number=number,
            enumeration_type=enumeration_type,
            taxonomy_description=taxonomy_description,
            first_name=first_name,
            last_name=last_name,
            organization_name=organization_name,
            address_purpose=address_purpose,
            city=city,
            state=state,
            postal_code=postal_code,
            country_code=country_code,
            limit=this_batch,
            skip=skip,
            version=version,
        )

        results = fetch_once(params)
        if not results:
            # No more data available
            break

        for record in results:
            yield record
            total_retrieved += 1

        # Prepare for next page
        skip += len(results)